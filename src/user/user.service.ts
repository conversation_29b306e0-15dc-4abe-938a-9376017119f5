import { BadRequestException, Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { User } from './entities/user.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import * as bcrypt from 'bcrypt';
import { userRegisterDto } from './dto/register.dto';
import { userLoginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class UserService {
  constructor(
    private em: EntityManager,
    private jwtService: JwtService,
    @InjectRedis() private readonly redis: Redis,
  ){}

  // =============== Helper Methods ==================
  private generateJwt(user: User): string {
    return this.jwtService.sign(
      { userId: user.id, email: user.email },
      { 
        secret: process.env.JWT_SECRET,
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
      }
    );
  }

  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.em
      .createQueryBuilder(User, 'user')  
      .where('user.email = :email', { email })
      .getOne();
    }

  private async normalizeEmail(email: string): Promise<string> {
    return email.toLowerCase().trim();
  }

  // =============== Registration ==================

  async register(registerDto: userRegisterDto) {
    const normalizedEmail = await this.normalizeEmail(registerDto.email);
    const user = await this.findByEmail(normalizedEmail);

    if (user) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_REGISTERED);
    }

    const otp = this.generateOtp();
    const hashedPassword = await bcrypt.hash(registerDto.password, 12);

    await this.redis.setex(
      `register:${normalizedEmail}`,
      600,
      JSON.stringify({
        email: normalizedEmail,
        password: hashedPassword,
        name: registerDto.name.trim(),
        otp
      })
    );
    console.log(`Registration OTP for ${normalizedEmail}: ${otp}`);

    return {
      message: 'OTP sent to your email. Please verify to complete registration.',
      email: normalizedEmail
    };

    // this part after usr eneter details shoudl route to verify email button 
    // i think we can remove this part more like there is verified email in table we can just use that part meaning 
    // onclick if email verfied == false return this new page verify email page which wills end otp 
  }

  async verifyRegistrationOtp(verifyOtpDto: {email: string, otp: string}) {
    const normalizedEmail = await this.normalizeEmail(verifyOtpDto.email);
    const registrationData = await this.redis.get(`register:${normalizedEmail}`);
    
    if (!registrationData) {
      throw new BadRequestException('Registration session expired. Please register again.');
    }

    const { email: userEmail, password, name, otp: storedOtp } = JSON.parse(registrationData);

    if (verifyOtpDto.otp !== storedOtp) {
      throw new BadRequestException('Invalid OTP. Please try again.');
    }

    // Double-check user doesn't exist 
    const existingUser = await this.findByEmail(userEmail);
    if (existingUser) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_REGISTERED);
    }

    // Create user with verified email
    const newUser = await this.em.save(User, {
      email: userEmail,
      password,
      name,
      email_verified: true,
      google_id: null
    });

    await this.redis.del(`register:${normalizedEmail}`);

    return {
      message: 'Registration completed successfully',
      access_token: this.generateJwt(newUser),
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        email_verified: true
      }
    };
  }

  // =============== Login ==================
  async login(loginDto: userLoginDto){
    const normalizedEmail = await this.normalizeEmail(loginDto.email);
    const user = await this.findByEmail(normalizedEmail);

    if (!user) {
      throw new BadRequestException(CustomErrorMessages.AUTH.INVALID_CREDENTIAL);
    }

    // this part after usr eneter details shoudl route to verify email button 
    // i think we can remove this part more like there is verified email in table we can just use that part meaning 
    // onclick if email verfied == false return this new page verify email page which wills end otp 
    if (!user.email_verified) {
      throw new BadRequestException('Please verify your email first');
    }

    if (!user.password) {
      throw new BadRequestException('This account uses Google login');
    }

    const isPasswordValid = await bcrypt.compare(loginDto.password, user.password);

    if (!isPasswordValid) {
      throw new BadRequestException(
        CustomErrorMessages.USER.INVALID_CREDENTIAL,
      );
    }

    return {
      message: 'Login successful',
      access_token: this.generateJwt(user),
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        email_verified: user.email_verified
      }
    };
  }

  // =============== Forgot Password ==================
  async forgotPassword(email: string) {
    const normalizedEmail = await this.normalizeEmail(email);
    const user = await this.findByEmail(normalizedEmail);

    if (!user || !user.password || !user.email_verified) {
      return { message: 'If the email exists, reset instructions have been sent.' };
    }

    const otp = this.generateOtp();
    
    // Store reset OTP (10 minutes)
    await this.redis.setex(
      `reset:${normalizedEmail}`,
      600,
      JSON.stringify({ otp })
    );

    // TODO: Send email with reset OTP

    console.log(`Password reset OTP for ${normalizedEmail}: ${otp}`);

    return { message: 'If the email exists, reset instructions have been sent.' };
  }

  async resetPassword(email: string, otp: string, newPassword: string) {
    const normalizedEmail = this.normalizeEmail(email);
    
    // Get reset OTP from Redis
    const resetData = await this.redis.get(`reset:${normalizedEmail}`);
    if (!resetData) {
      throw new BadRequestException('Invalid or expired reset code');
    }

    const { otp: storedOtp } = JSON.parse(resetData);
    
    if (storedOtp !== otp) {
      throw new BadRequestException('Invalid reset code');
    }

    // Update password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    await this.em
      .createQueryBuilder()
      .update(User)
      .set({ password: hashedPassword })
      .where('email = :email', { email: normalizedEmail })
      .execute();

    // Clean up Redis
    await this.redis.del(`reset:${normalizedEmail}`);

    return { message: 'Password reset successfully' };
  }




  

}
