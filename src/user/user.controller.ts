import { ApiOperation } from '@nestjs/swagger';
import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { UserService } from './user.service';
import { userLoginDto } from './dto/login.dto';
import { userRegisterDto } from './dto/register.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';


@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  // register 
  @Post('register')
  async register(@Body() userRegisterDto: userRegisterDto) {
    return await this.userService.register(userRegisterDto);
  }

  // verify-registration-otp
  @Post('verify-otp')
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    return this.userService.verifyRegistrationOtp(verifyOtpDto);
  }

  // login 
  @Post('login')
  async login(@Body() userLoginDto: userLoginDto) {
    return await this.userService.login(userLoginDto);
  }

  @Post('forgot-password')
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    return this.userService.forgotPassword(dto.email);
  }

  @Post('reset-password')
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return this.userService.resetPassword(dto.email, dto.otp, dto.new_password);
  }



  // verify-otp 

  // forgot-password 

  // verify-forgot-password-otp 

  // reset-password 

  // add-password

  // change-password

  // profile

  // logout
  
}
