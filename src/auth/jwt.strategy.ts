import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { User } from 'src/user/entities/user.entity';
import { EntityManager } from 'typeorm';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly em: EntityManager) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(payload: any) {
    const user = await this.em
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: payload.userId })
      .getOne();

    // IMPORTANT: Only allow verified users to pass auth guard
    if (!user || !user.email_verified) {
      throw new UnauthorizedException('Email verification required');
    }

    return {
      userId: user.id,
      email: user.email,
      name: user.name,
      email_verified: user.email_verified
    };
  }
}