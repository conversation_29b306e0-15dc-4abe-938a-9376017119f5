export const CustomErrorMessages = {
  AUTH: {
    INVALID_CREDENTIAL: 'Invalid credential',
    RESET_PASSWORD: 'Please reset password to login',
    ACCOUNT_NOT_FOUND: 'Account not exists',
    EMAIL_NOT_VERIFY: 'Email not verify',
    NOT_LOGIN: 'Must be login before perform any action',
    TOKEN_NOT_FOUND: 'Please provide valid token',
    INVALID_TOKEN: 'Invalid token',
    TOKEN_EXPIRED: 'Token is expired',
    INVALID_USER: 'User is not valid',
    INVALID_ADMIN: 'Admin is not valid',
    INVALID_TOKEN_ISSUER: 'Token issuer is not valid',
    BEARER_NOT_FOUND: 'Authorization header is missing',
    INVALID_BEARER: 'Invalid authorization header format',
    PAYLOAD_MISSING: 'Authentication payload is missing',
    EXPIRED_MISSING: 'Authentication payload expired time is missing',
    INVALID_TOKEN_PAYLOAD: 'Invalid token payload',
    INVALID_ENCRYPTED_TEXT: 'Invalid encrypted text format',
  },
  COMMON: {
    INVALID_INPUT: 'Input contain invalid entry. Please retry.',
    INVALID_KEY: 'Invalid old public key, regenerate denied.',
    MISSING_KEY: 'Please input the old public key to verify',
    MISSING_SIGNATURE: 'Signature not provided',
    INVALID_SIGNATURE: 'Invalid Signature',
    INVALID_2FA: 'Invalid 2FA',
    MISSING_SETUP:
      "System keypair not found, please generate through '/generate-public-key' route.",
  },
  USER: {
    GOOGLE_BINDED: 'This user had binded 2fa before',
    GOOGLE_NOT_BINDED: 'This user had not bind 2fa yet',
    USER_NOT_FOUND: 'User has not registered',
    USER_REGISTERED: 'User has been register before',
    INVALID_CREDENTIAL: 'Invalid email or password',
    INVALID_PASSWORD: 'Invalid password',
    SESSION_NOT_MATCH: 'Email is not match with session',
  },
  BOT_TYPE: {
    INVALID_BOT_TYPE: 'Invalid Bot Type ID',
  },
  BATCH: {
    INVALID_BATCH: 'Invalid Batch ID',
  },
  BOT: {
    INVALID_STATUS: 'Invalid Status',
    UNKNOWN_BOT: 'Bot Not Found',
    INVALID_UUID: 'Invalid uuid',
    SAME_PAIR_USED: 'Same pair is already used in another active bot',
    INVALID_USER_KEY: 'Invalid user key',
    DUPLICATE_PAIR: 'Base pair and target pair cannot be the same',
    BASE_ASSET_MISMATCH: 'Base asset of both pairs must be the same',
    PAIR_ALREADY_ACTIVE: 'Same pair is already used in another active bot',
    UNSUPPORTED_TYPE:
      'Maker arbitrage is not ready at the moment, please use taker bot instead',
    INSUFFICIENT_FUNDS: 'Insufficient fund to start bot.',
    INVALID_PERCENTAGE: 'Percentage must be greater than 0',
  },
  BOT_CONFIG: {
    INVALID_BOTCONFIG_ID: 'Invalid Bot Configuration ID',
    INVALID_BOTCONFIG_BATCH: 'No batch found for Bot Configuration',
  },
  PROFIT: {
    ORDER_RECORD_EXIST: 'Order record already exist in database',
    PROFIT_NOT_FOUND: 'Profit not found',
  },
  PAIR: {
    INVALID_PAIR: 'Invalid Pair ID',
    INVALID_NOTIONAL: 'Invalid notional value for pair',
    PAIR_NOT_FOUND: 'Pair Not Found',
    NO_SYMBOLS_HATA: 'No symbols received from Hata exchange info',
    SEED_FAILED_BINANCE: 'Failed to seed pairs from Binance',
    SEED_FAILED_HATA: 'Failed to seed pairs from Hata',
  },
  TRADING_PAIR: {
    INVALID_TRADINGPAIR_ID: 'Invalid Trading Pair ID',
  },
  USER_KEY: {
    INVALID_USERKEY: 'Invalid api or secret key',
    USER_KEY_NOT_FOUND: 'Key Not Found',
    USER_KEY_DUPLICATE: 'User key duplicate',
    INACTIVE_OR_DELETED: (operation: string) =>
      `Cannot ${operation}: User key is not active or has been deleted`,
  },
  PLATFORM: {
    USERKEY_NOT_LOADED: 'Api or secret key not loaded',
    INVALID_LOCALE: 'Unsupported locale',
    INVALID_PLATFORM: 'Unsupported platform',
    NOT_FOUND_PLATFORM: 'Platform not found',
    NOT_FOUND_BINANCE:
      'Platform binance not found, please make sure it is supported in system',
    NOT_FOUND_HATA:
      'Platform Hata not found, please ensure it is supported in the system',
  },
  RATE: {
    INVALID_RATE_ID: 'Invalid Rate ID',
    INVALID_SYMBOL: 'Invalid symbol',
    INVALID_PASSWORD: 'Invalid password',
    INVALID_RATE_VALUE: 'Invalid rate value',
  },
  IMAGE: {
    MAX_SIZE: (size) => `File size too large. Maximum size is ${size}MB`,
    FILE_TYPE: (type) => `Invalid file type. Allowed types are: ${type}`,
    MAX_FILE: (file) => `Too many files. Maximum ${file} files allowed`,
  },
};
